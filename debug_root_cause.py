#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from server.new_db_handler import DatabaseHandler
from server.email_service import EmailService
from server.spam_filter import SpamFilter


def investigate_root_cause():
    print("=== 深入调查垃圾邮件过滤问题的根源 ===\n")

    # 1. 直接查询数据库
    print("1. 检查数据库中的奖金发放邮件状态:")
    conn = sqlite3.connect("data/email_db.sqlite")
    cursor = conn.cursor()

    cursor.execute(
        "SELECT id, subject, is_spam, spam_score, message_id FROM emails WHERE subject LIKE '%奖金发放%'"
    )
    rows = cursor.fetchall()

    if not rows:
        print("   未找到奖金发放邮件")
        conn.close()
        return

    email_info = rows[0]
    email_id, subject, is_spam, spam_score, message_id = email_info
    print(f"   ID: {email_id}")
    print(f"   主题: {subject}")
    print(f"   is_spam: {is_spam}")
    print(f"   spam_score: {spam_score}")
    print(f"   message_id: {message_id}")

    conn.close()

    # 2. 检查.eml文件
    print(f"\n2. 检查.eml文件:")
    eml_path = f"data/emails/{message_id}.eml"
    print(f"   文件路径: {eml_path}")
    print(f"   文件存在: {os.path.exists(eml_path)}")

    if os.path.exists(eml_path):
        with open(eml_path, "r", encoding="utf-8") as f:
            eml_content = f.read()
        print(f"   文件大小: {len(eml_content)} 字符")
        print(f"   内容预览: {eml_content[:300]}...")

        # 3. 解析邮件内容
        print(f"\n3. 解析邮件内容:")
        email_service = EmailService()
        try:
            parsed = email_service.parse_email_content(eml_content)
            body = parsed.get("body", "")
            print(f"   解析成功: True")
            print(f"   正文长度: {len(body)} 字符")
            print(f"   正文内容: {body[:200]}...")

            # 4. 测试垃圾邮件过滤器
            print(f"\n4. 测试垃圾邮件过滤器:")
            spam_filter = SpamFilter()

            # 获取发件人信息
            cursor = conn = sqlite3.connect("data/email_db.sqlite")
            cursor = conn.cursor()
            cursor.execute("SELECT from_addr FROM emails WHERE id = ?", (email_id,))
            from_addr = cursor.fetchone()[0]
            conn.close()

            test_email = {"from_addr": from_addr, "subject": subject, "content": body}

            result = spam_filter.analyze_email(test_email)
            print(f"   过滤器分析结果: {result}")
            print(f"   应该是垃圾邮件: {result['is_spam']}")
            print(f"   垃圾邮件评分: {result['score']}")

            # 5. 检查保存邮件的流程
            print(f"\n5. 检查save_email方法的调用:")
            print("   让我们追踪邮件是如何被保存的...")

        except Exception as e:
            print(f"   解析失败: {e}")

    # 6. 检查邮件是何时被保存的
    print(f"\n6. 检查邮件保存时间和方式:")
    conn = sqlite3.connect("data/email_db.sqlite")
    cursor = conn.cursor()
    cursor.execute("SELECT created_at FROM emails WHERE id = ?", (email_id,))
    created_at = cursor.fetchone()[0]
    print(f"   创建时间: {created_at}")
    conn.close()

    print(f"\n=== 结论分析 ===")
    print("问题可能的原因:")
    print("1. 邮件保存时垃圾邮件检测没有正确执行")
    print("2. 垃圾邮件检测结果没有正确保存到数据库")
    print("3. 邮件内容在保存时和检测时不一致")
    print("4. save_email方法中的逻辑有问题")


if __name__ == "__main__":
    investigate_root_cause()
