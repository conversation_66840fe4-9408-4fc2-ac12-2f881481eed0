#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime
sys.path.append('.')

def test_spam_fix_final():
    """测试垃圾过滤修复的最终效果"""
    print("=== 测试垃圾过滤修复效果 ===\n")
    
    try:
        # 1. 创建一个测试垃圾邮件对象
        print("1. 创建测试垃圾邮件对象:")
        from common.models import Email, EmailAddress
        
        test_spam_email = Email(
            message_id=f"<spam_test_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}@test.com>",
            subject="奖金发放通知 - 恭喜中奖",
            from_addr=EmailAddress("", "<EMAIL>"),
            to_addrs=[EmailAddress("", "<EMAIL>")],
            cc_addrs=[],
            bcc_addrs=[],
            date=datetime.datetime.now(),
            text_content="恭喜您中奖了！请立即点击领取您的奖金！限时优惠，机不可失！",
            html_content="<p>恭喜您中奖了！请立即点击领取您的奖金！限时优惠，机不可失！</p>",
            attachments=[],
        )
        
        print(f"   测试邮件主题: {test_spam_email.subject}")
        print(f"   测试邮件内容: {test_spam_email.text_content[:50]}...")
        
        # 2. 测试垃圾过滤器直接分析
        print("\n2. 测试垃圾过滤器直接分析:")
        from spam_filter.spam_filter import KeywordSpamFilter
        
        spam_filter = KeywordSpamFilter()
        
        # 分析邮件
        analysis_data = {
            "from_addr": test_spam_email.from_addr.address,
            "subject": test_spam_email.subject,
            "content": test_spam_email.text_content
        }
        
        result = spam_filter.analyze_email(analysis_data)
        print(f"   垃圾过滤器判定: {'垃圾邮件' if result['is_spam'] else '正常邮件'}")
        print(f"   垃圾评分: {result['score']:.1f}")
        print(f"   匹配关键词: {result['matched_keywords']}")
        
        # 3. 测试通过数据库保存
        print("\n3. 测试通过数据库保存:")
        from server.new_db_handler import DatabaseHandler
        from common.email_format_handler import EmailFormatHandler
        
        db_handler = DatabaseHandler()
        
        # 生成EML内容
        eml_content = EmailFormatHandler.format_email_for_storage(test_spam_email)
        
        # 保存邮件（使用修复后的逻辑）
        success = db_handler.save_email(
            message_id=test_spam_email.message_id,
            from_addr=test_spam_email.from_addr.address,
            to_addrs=[addr.address for addr in test_spam_email.to_addrs],
            subject=test_spam_email.subject,
            content=test_spam_email.text_content,  # 纯文本内容用于垃圾过滤
            full_content_for_storage=eml_content,  # 完整EML内容用于存储
            date=test_spam_email.date,
        )
        
        print(f"   数据库保存结果: {success}")
        
        if success:
            # 4. 从数据库检索并验证
            print("\n4. 从数据库检索并验证:")
            saved_email = db_handler.get_email(test_spam_email.message_id)
            
            if saved_email:
                print(f"   邮件主题: {saved_email['subject']}")
                print(f"   是否垃圾邮件: {saved_email['is_spam']}")
                print(f"   垃圾评分: {saved_email['spam_score']}")
                
                # 验证垃圾过滤是否正确工作
                if saved_email['is_spam'] == result['is_spam']:
                    print("   ✅ 垃圾过滤功能正常工作！")
                else:
                    print("   ❌ 垃圾过滤功能异常")
                    print(f"      预期: {result['is_spam']}, 实际: {saved_email['is_spam']}")
            else:
                print("   ❌ 无法从数据库检索邮件")
        
        # 5. 测试过滤查询功能
        print("\n5. 测试过滤查询功能:")
        
        # 测试仅显示正常邮件
        normal_emails = db_handler.list_emails(include_spam=False, is_spam=False, limit=10)
        print(f"   仅正常邮件查询: {len(normal_emails)} 封")
        
        # 检查结果中是否有垃圾邮件
        spam_in_normal = [email for email in normal_emails if email.get('is_spam', False)]
        if spam_in_normal:
            print(f"   ❌ 正常邮件查询中包含 {len(spam_in_normal)} 封垃圾邮件")
        else:
            print(f"   ✅ 正常邮件查询结果正确")
        
        # 测试仅显示垃圾邮件
        spam_emails = db_handler.list_emails(include_spam=True, is_spam=True, limit=10)
        print(f"   仅垃圾邮件查询: {len(spam_emails)} 封")
        
        # 检查结果中是否有正常邮件
        normal_in_spam = [email for email in spam_emails if not email.get('is_spam', True)]
        if normal_in_spam:
            print(f"   ❌ 垃圾邮件查询中包含 {len(normal_in_spam)} 封正常邮件")
        else:
            print(f"   ✅ 垃圾邮件查询结果正确")
        
        # 6. 显示垃圾邮件列表
        if spam_emails:
            print(f"\n6. 当前垃圾邮件列表:")
            for email in spam_emails:
                print(f"   - {email.get('subject', '')[:50]}... [评分:{email.get('spam_score', 0):.1f}]")
        
        print("\n=== 测试完成 ===")
        print("\n💡 现在你可以在CLI中测试:")
        print("   1. 运行 python cli.py")
        print("   2. 选择 '收件箱'")
        print("   3. 选择 '2. 仅显示正常邮件' - 应该不显示垃圾邮件")
        print("   4. 选择 '3. 仅显示垃圾邮件' - 应该只显示垃圾邮件")
        print("   5. 垃圾邮件会显示 '🚫垃圾' 标记和 '[垃圾]' 前缀")
        
        print("\n🔧 如果要接收新邮件并自动进行垃圾过滤:")
        print("   1. 运行 python cli.py")
        print("   2. 选择 '接收邮件'")
        print("   3. 选择 '接收所有邮件' 或 '接收最新邮件'")
        print("   4. 新邮件会自动进行垃圾检测并正确标记")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_spam_fix_final()
